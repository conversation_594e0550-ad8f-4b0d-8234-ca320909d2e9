import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import * as XLSX from "xlsx";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { user } = auth;
    const userRole = user.role;

    // Check if user has financial reporting access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { reportType, reportData, format, filename } = body;

    if (!reportType || !reportData || !format) {
      return NextResponse.json(
        { error: "Missing required parameters: reportType, reportData, format" },
        { status: 400 }
      );
    }

    if (!["csv", "xlsx"].includes(format)) {
      return NextResponse.json(
        { error: "Invalid format. Must be 'csv' or 'xlsx'" },
        { status: 400 }
      );
    }

    let exportData: any[] = [];
    let worksheetName = "Financial Report";
    let exportFilename = filename || `financial-report-${new Date().toISOString().split('T')[0]}`;

    // Process different report types
    switch (reportType) {
      case "daily-sales":
        exportData = processDailySalesReport(reportData);
        worksheetName = "Daily Sales Report";
        exportFilename = `daily-sales-${reportData.reportDate}`;
        break;

      case "periodic-sales":
        exportData = processPeriodicSalesReport(reportData);
        worksheetName = `${reportData.reportType.charAt(0).toUpperCase() + reportData.reportType.slice(1)} Sales Report`;
        exportFilename = `${reportData.reportType}-sales-${reportData.period.start}-to-${reportData.period.end}`;
        break;

      case "profit-analysis":
        exportData = processProfitAnalysisReport(reportData);
        worksheetName = "Profit Analysis";
        exportFilename = `profit-analysis-${reportData.period.start}-to-${reportData.period.end}`;
        break;

      case "inventory-valuation":
        exportData = processInventoryValuationReport(reportData);
        worksheetName = "Inventory Valuation";
        exportFilename = `inventory-valuation-${reportData.reportDate}`;
        break;

      default:
        return NextResponse.json(
          { error: "Unsupported report type" },
          { status: 400 }
        );
    }

    if (format === "csv") {
      // Generate CSV
      const csv = generateCSV(exportData);
      
      return new NextResponse(csv, {
        status: 200,
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="${exportFilename}.csv"`,
        },
      });
    } else {
      // Generate Excel
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      
      // Auto-size columns
      const colWidths = Object.keys(exportData[0] || {}).map(key => ({
        wch: Math.max(key.length, 15)
      }));
      worksheet['!cols'] = colWidths;
      
      XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName);
      
      const excelBuffer = XLSX.write(workbook, { 
        type: "buffer", 
        bookType: "xlsx" 
      });

      return new NextResponse(excelBuffer, {
        status: 200,
        headers: {
          "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "Content-Disposition": `attachment; filename="${exportFilename}.xlsx"`,
        },
      });
    }

  } catch (error) {
    console.error("Error exporting financial report:", error);
    return NextResponse.json(
      {
        error: "Failed to export financial report",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

function processDailySalesReport(data: any): any[] {
  const exportData = [];

  // Summary section
  exportData.push({
    Section: "SUMMARY",
    Metric: "Total Revenue",
    Value: data.summary.totalRevenue,
    Unit: "IDR",
    Date: data.reportDate,
  });
  exportData.push({
    Section: "SUMMARY",
    Metric: "Total Profit",
    Value: data.summary.totalProfit,
    Unit: "IDR",
    Date: data.reportDate,
  });
  exportData.push({
    Section: "SUMMARY",
    Metric: "Total Transactions",
    Value: data.summary.totalTransactions,
    Unit: "Count",
    Date: data.reportDate,
  });
  exportData.push({
    Section: "SUMMARY",
    Metric: "Average Order Value",
    Value: data.summary.averageOrderValue,
    Unit: "IDR",
    Date: data.reportDate,
  });
  exportData.push({
    Section: "SUMMARY",
    Metric: "Profit Margin",
    Value: data.summary.profitMargin,
    Unit: "%",
    Date: data.reportDate,
  });

  // Add empty row
  exportData.push({});

  // Payment method breakdown
  data.paymentMethodBreakdown.forEach((payment: any) => {
    exportData.push({
      Section: "PAYMENT METHODS",
      "Payment Method": payment.method,
      "Transaction Count": payment.count,
      "Total Amount": payment.amount,
      "Percentage": payment.percentage,
      Date: data.reportDate,
    });
  });

  // Add empty row
  exportData.push({});

  // Top products
  data.topProducts.forEach((product: any, index: number) => {
    exportData.push({
      Section: "TOP PRODUCTS",
      Rank: index + 1,
      "Product Name": product.productName,
      SKU: product.sku,
      "Quantity Sold": product.quantitySold,
      Revenue: product.revenue,
      Profit: product.profit,
      Date: data.reportDate,
    });
  });

  return exportData;
}

function processPeriodicSalesReport(data: any): any[] {
  const exportData = [];

  // Summary section
  exportData.push({
    Section: "SUMMARY",
    Period: data.period.label,
    "Total Revenue": data.summary.totalRevenue,
    "Total Profit": data.summary.totalProfit,
    "Total Transactions": data.summary.totalTransactions,
    "Average Order Value": data.summary.averageOrderValue,
    "Profit Margin": data.summary.profitMargin,
    "Average Daily Revenue": data.summary.averageDailyRevenue,
  });

  // Add empty row
  exportData.push({});

  // Daily breakdown
  data.dailyBreakdown.forEach((day: any) => {
    exportData.push({
      Section: "DAILY BREAKDOWN",
      Date: day.date,
      Revenue: day.revenue,
      Profit: day.profit,
      Transactions: day.transactions,
      "Average Order Value": day.averageOrderValue,
    });
  });

  // Add empty row
  exportData.push({});

  // Category performance
  data.categoryPerformance.forEach((category: any) => {
    exportData.push({
      Section: "CATEGORY PERFORMANCE",
      Category: category.categoryName,
      Revenue: category.revenue,
      Profit: category.profit,
      Transactions: category.transactions,
      "Profit Margin": category.profitMargin,
    });
  });

  return exportData;
}

function processProfitAnalysisReport(data: any): any[] {
  const exportData = [];

  // Overall metrics
  exportData.push({
    Section: "OVERALL METRICS",
    "Total Revenue": data.overallMetrics.totalRevenue,
    "Total Cost": data.overallMetrics.totalCost,
    "Total Profit": data.overallMetrics.totalProfit,
    "Overall Profit Margin": data.overallMetrics.overallProfitMargin,
    "Average Profit Per Transaction": data.overallMetrics.averageProfitPerTransaction,
    "Average Profit Per Item": data.overallMetrics.averageProfitPerItem,
  });

  // Add empty row
  exportData.push({});

  // Product analysis
  data.productAnalysis.forEach((product: any) => {
    exportData.push({
      Section: "PRODUCT ANALYSIS",
      "Product Name": product.productName,
      SKU: product.sku,
      Category: product.category,
      "Quantity Sold": product.quantitySold,
      Revenue: product.revenue,
      Cost: product.cost,
      Profit: product.profit,
      "Profit Margin": product.profitMargin,
      "Average Selling Price": product.averageSellingPrice,
      "Average Cost": product.averageCost,
      "Profit Per Unit": product.profitPerUnit,
    });
  });

  return exportData;
}

function processInventoryValuationReport(data: any): any[] {
  const exportData = [];

  // Summary
  exportData.push({
    Section: "SUMMARY",
    "Total Inventory Value": data.summary.totalInventoryValue,
    "Total Store Value": data.summary.totalStoreValue,
    "Total Warehouse Value": data.summary.totalWarehouseValue,
    "Total Products": data.summary.totalProducts,
    "Total Batches": data.summary.totalBatches,
    "Average Value Per Product": data.summary.averageValuePerProduct,
    "Valuation Method": data.valuationMethod,
  });

  // Add empty row
  exportData.push({});

  // Category valuation
  data.categoryValuation.forEach((category: any) => {
    exportData.push({
      Section: "CATEGORY VALUATION",
      Category: category.categoryName,
      "Total Value": category.totalValue,
      "Product Count": category.productCount,
      "Batch Count": category.batchCount,
      "Percentage": category.percentage,
      "Average Value Per Product": category.averageValuePerProduct,
    });
  });

  return exportData;
}

function generateCSV(data: any[]): string {
  if (data.length === 0) return "";

  const headers = Object.keys(data[0]);
  const csvRows = [headers.join(",")];

  data.forEach(row => {
    const values = headers.map(header => {
      const value = row[header];
      // Escape commas and quotes in CSV
      if (typeof value === "string" && (value.includes(",") || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value || "";
    });
    csvRows.push(values.join(","));
  });

  return csvRows.join("\n");
}
