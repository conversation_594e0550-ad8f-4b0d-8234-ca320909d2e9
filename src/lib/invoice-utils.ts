import { prisma } from '@/lib/prisma';

/**
 * Note: Invoice number generation has been moved to the server-side API endpoint:
 * /api/invoices/generate-number
 *
 * This prevents "PrismaClient is unable to run in this browser environment" errors
 * when the function is called from client-side components.
 */

/**
 * Validate invoice number format
 */
export function validateInvoiceNumber(invoiceNumber: string): boolean {
  const pattern = /^INV-\d{4}-\d{2}-\d{4}$/;
  return pattern.test(invoiceNumber);
}

/**
 * Calculate invoice totals
 */
export function calculateInvoiceTotals(
  items: Array<{ quantity: number; unitPrice: number }>,
  taxPercentage: number = 0
) {
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const tax = (subtotal * taxPercentage) / 100;
  const total = subtotal + tax;

  return {
    subtotal: Math.round(subtotal * 100) / 100,
    tax: Math.round(tax * 100) / 100,
    total: Math.round(total * 100) / 100,
  };
}

/**
 * Get invoice status color for UI
 */
export function getInvoiceStatusColor(status: string): string {
  switch (status) {
    case 'PENDING':
      return 'yellow';
    case 'APPROVED':
      return 'green';
    case 'REJECTED':
      return 'red';
    case 'CANCELLED':
      return 'gray';
    default:
      return 'gray';
  }
}

/**
 * Get payment status color for UI
 */
export function getPaymentStatusColor(status: string): string {
  switch (status) {
    case 'UNPAID':
      return 'red';
    case 'PARTIALLY_PAID':
      return 'yellow';
    case 'PAID':
      return 'green';
    case 'OVERDUE':
      return 'red';
    default:
      return 'gray';
  }
}

/**
 * Check if invoice is overdue
 */
export function isInvoiceOverdue(dueDate: Date | null, paymentStatus: string): boolean {
  if (!dueDate || paymentStatus === 'PAID') {
    return false;
  }
  
  return new Date() > dueDate;
}

/**
 * Calculate days until due or overdue
 */
export function getDaysUntilDue(dueDate: Date | null): number | null {
  if (!dueDate) {
    return null;
  }
  
  const today = new Date();
  const diffTime = dueDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency: string = 'IDR'): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Get invoice summary statistics
 */
export async function getInvoiceSummaryStats(supplierId?: string) {
  const where = supplierId ? { supplierId } : {};
  
  const [
    totalInvoices,
    pendingInvoices,
    approvedInvoices,
    paidInvoices,
    overdueInvoices,
    totalAmount,
    paidAmount,
  ] = await Promise.all([
    prisma.invoice.count({ where }),
    prisma.invoice.count({ where: { ...where, status: 'PENDING' } }),
    prisma.invoice.count({ where: { ...where, status: 'APPROVED' } }),
    prisma.invoice.count({ where: { ...where, paymentStatus: 'PAID' } }),
    prisma.invoice.count({ 
      where: { 
        ...where, 
        paymentStatus: 'OVERDUE' 
      } 
    }),
    prisma.invoice.aggregate({
      where,
      _sum: { total: true }
    }),
    prisma.invoice.aggregate({
      where,
      _sum: { paidAmount: true }
    }),
  ]);

  return {
    totalInvoices,
    pendingInvoices,
    approvedInvoices,
    paidInvoices,
    overdueInvoices,
    totalAmount: Number(totalAmount._sum.total || 0),
    paidAmount: Number(paidAmount._sum.paidAmount || 0),
    unpaidAmount: Number(totalAmount._sum.total || 0) - Number(paidAmount._sum.paidAmount || 0),
  };
}

/**
 * Update invoice payment status based on payments
 */
export async function updateInvoicePaymentStatus(invoiceId: string) {
  const invoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: {
      payments: true
    }
  });

  if (!invoice) {
    throw new Error('Invoice not found');
  }

  const totalPaid = invoice.payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
  const totalAmount = Number(invoice.total);
  
  let paymentStatus: 'UNPAID' | 'PARTIALLY_PAID' | 'PAID' | 'OVERDUE' = 'UNPAID';
  
  if (totalPaid >= totalAmount) {
    paymentStatus = 'PAID';
  } else if (totalPaid > 0) {
    paymentStatus = 'PARTIALLY_PAID';
  } else if (invoice.dueDate && new Date() > invoice.dueDate) {
    paymentStatus = 'OVERDUE';
  }

  await prisma.invoice.update({
    where: { id: invoiceId },
    data: {
      paidAmount: totalPaid,
      paymentStatus,
      paidAt: paymentStatus === 'PAID' ? new Date() : null,
    }
  });

  return paymentStatus;
}
