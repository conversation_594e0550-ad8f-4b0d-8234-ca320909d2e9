// Simple test script to test the export API
const testExport = async () => {
  try {
    const response = await fetch("http://localhost:3001/api/reports/financial/export", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        reportType: "daily-sales",
        reportData: {
          reportDate: "2025-06-23",
          summary: {
            totalRevenue: 1000000,
            totalProfit: 200000,
            totalTransactions: 10,
            averageOrderValue: 100000,
            profitMargin: 20
          },
          paymentMethodBreakdown: [
            { method: "Cash", count: 5, amount: 500000, percentage: 50 },
            { method: "Card", count: 5, amount: 500000, percentage: 50 }
          ],
          topProducts: [
            { productName: "Test Product", sku: "TEST001", quantitySold: 5, revenue: 500000, profit: 100000 }
          ]
        },
        format: "csv",
        filename: "test-export",
      }),
    });

    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const blob = await response.blob();
      console.log("Export successful! Blob size:", blob.size);
    } else {
      const error = await response.text();
      console.log("Export failed:", error);
    }
  } catch (error) {
    console.error("Error:", error);
  }
};

testExport();
